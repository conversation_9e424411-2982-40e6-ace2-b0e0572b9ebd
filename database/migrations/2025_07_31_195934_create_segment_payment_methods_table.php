<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('segment_payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('segment_id')->constrained();
            $table->foreignId('payment_method_id')->constrained();
            $table->boolean('allowed_in_first_order')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('segment_payment_methods');
    }
};
