<?php

namespace App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;

use App\Filament\Clusters\AccessControl\Resources\UserResource;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    /** @inheritDoc */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\User $user */
        $user = User::find($this->record->id);

        return array_merge($data, ['role' => $user->getRoleNames()[0]]);
    }
}
