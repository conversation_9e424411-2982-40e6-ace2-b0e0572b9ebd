<?php

namespace App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;

use App\Filament\Clusters\AccessControl\Resources\UserResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    /** @inheritDoc */
    protected function handleRecordCreation(array $data): Model
    {
        return \App\Actions\User\CreateUser::run($data);
    }

    /** @inheritDoc */
    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('users.responses.create.success'));
    }
}
