<?php

namespace App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;

use App\Actions\User\DeleteUser;
use App\Enums\RoleEnum;
use App\Filament\Clusters\AccessControl\Resources\UserResource;
use App\Models\User;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->using(fn(User $user): bool => DeleteUser::run($user))
                ->hidden(fn(User $user): bool => $user->id === auth()->id())
                ->successNotification(success_notification(__('users.responses.delete.success'))),
        ];
    }

    protected function beforeValidate(): void
    {
        if ($this->record->active) {
            return;
        }

        $maxUsersCount = User::query()
            ->where('active', true)
            ->whereRelation('roles', fn(Builder $query): Builder => $query->where('name', '<>', RoleEnum::CustomerUser->value))
            ->get()
            ->count();

        if ($maxUsersCount === tenant('max_user_count')) {
            error_notification('Não é possível ativar o usuário pois o número máximo de usuários ativos foi alcançado.')->send();
            $this->halt();
        }
    }

    /** @inheritDoc */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\User $user */
        $user = User::find($this->record->id);

        return array_merge($data, ['role' => $user->getRoleNames()[0]]);
    }

    /** @inheritDoc */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return \App\Actions\User\EditUser::run($record, $data);
    }

    /** @inheritDoc */
    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('users.responses.update.success'));
    }
}
