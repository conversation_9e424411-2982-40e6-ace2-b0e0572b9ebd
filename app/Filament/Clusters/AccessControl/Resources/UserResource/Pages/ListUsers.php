<?php

namespace App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;

use App\Actions\User\GetThirdPartyUsers;
use App\Filament\Clusters\AccessControl\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Throwable;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('get-third-party-users')
                ->label('Integrar usuários')
                ->color('gray')
                ->action(function () {
                    try {
                        GetThirdPartyUsers::run(true);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.access-control.resources.users.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar os usuários neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            Actions\CreateAction::make(),
        ];
    }
}
