<?php

namespace App\Filament\Clusters\AccessControl\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Table;

class SmtpConfigurationsRelationManager extends RelationManager
{
    protected static string $relationship = 'userSmtpConfigurations';
    protected static ?string $title = 'Configurações SMTP';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Grid::make(4)->schema([
                Forms\Components\TextInput::make('host')
                    ->label(__('smtp_configurations.forms.fields.host'))
                    ->required()
                    ->columnSpan(3),
                Forms\Components\TextInput::make('port')
                    ->label(__('smtp_configurations.forms.fields.port'))
                    ->required()
                    ->numeric(),
            ]),
            Forms\Components\Grid::make(4)->schema([
                Forms\Components\TextInput::make('username')
                    ->label(__('smtp_configurations.forms.fields.username'))
                    ->required()
                    ->columnSpan(2),
                Forms\Components\TextInput::make('password')
                    ->label(__('smtp_configurations.forms.fields.password'))
                    ->required(),
                Forms\Components\TextInput::make('encryption')
                    ->label(__('smtp_configurations.forms.fields.encryption'))
                    ->required(),
            ]),
            Forms\Components\Grid::make(4)->schema([
                Forms\Components\TextInput::make('from_address')
                    ->label(__('smtp_configurations.forms.fields.from_address'))
                    ->required()
                    ->columnSpan(3),
                Forms\Components\TextInput::make('from_name')
                    ->label(__('smtp_configurations.forms.fields.from_name'))
                    ->required(),
            ]),
            Forms\Components\Grid::make(1)->schema([
                Forms\Components\Toggle::make('active')
                    ->label(__('smtp_configurations.forms.fields.active')),
            ])
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Configurações SMTP')
            ->modelLabel('configuração SMTP')
            ->columns([
                Tables\Columns\TextColumn::make('id')->label(__('smtp_configurations.forms.fields.id')),
                Tables\Columns\TextColumn::make('host')->label(__('smtp_configurations.forms.fields.host')),
                Tables\Columns\TextColumn::make('username')->label(__('smtp_configurations.forms.fields.username')),
                Tables\Columns\IconColumn::make('active')
                    ->label(__('smtp_configurations.forms.fields.active'))
                    ->boolean()
            ])
            ->filters([])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make(),
                    DeleteAction::make()
                ])
            ])
            ->bulkActions([])
            ->emptyStateActions([
                CreateAction::make(),
            ])
            ->emptyStateHeading('Ainda sem configurações SMTP')
            ->emptyStateDescription('Assim que você cadastrar suas configurações SMTP, elas aparecerão nesta listagem.');
    }
}
