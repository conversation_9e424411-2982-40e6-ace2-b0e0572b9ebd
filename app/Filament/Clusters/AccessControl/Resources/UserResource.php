<?php

namespace App\Filament\Clusters\AccessControl\Resources;

use App\Actions\Customer\GetCustomersByNameTradingNameOrTaxIdentificationNumber;
use App\Actions\User\DeleteUser;
use App\Enums\RoleEnum;
use App\Filament\Clusters\AccessControl;
use App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;
use App\Filament\Clusters\AccessControl\Resources\UserResource\RelationManagers\SmtpConfigurationsRelationManager;
use App\Models\Customer;
use App\Models\User;
use App\Models\UserCustomer;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $cluster = AccessControl::class;
    protected static ?string $modelLabel = 'usuário';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationIcon = 'heroicon-o-user';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Identificação')->schema([
                        Grid::make(1)->schema([
                            Toggle::make('active')
                                ->label(__('users.forms.fields.active'))
                                ->default(true),
                        ]),
                        Grid::make(1)->schema([
                            TextInput::make('name')
                                ->label(__('users.forms.fields.name'))
                                ->required(),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('email')
                                ->label(__('users.forms.fields.email'))
                                ->required()
                                ->columnSpan([
                                    'xs' => 4,
                                    'sm' => 4,
                                    'md' => 4,
                                    'lg' => 1,
                                    'xl' => 1,
                                ]),
                            TextInput::make('password')
                                ->label(__('users.forms.fields.password'))
                                ->columnSpan([
                                    'xs' => 4,
                                    'sm' => 4,
                                    'md' => 4,
                                    'lg' => 1,
                                    'xl' => 1,
                                ]),
                            TextInput::make('phone_1')
                                ->label(__('users.forms.fields.phone_1'))
                                ->mask(RawJs::make(<<<'JS'
                                    $input.length > 10 ? '(99) 99999-9999' : '(99) 9999-9999'
                                JS))
                                ->columnSpan([
                                    'xs' => 4,
                                    'sm' => 4,
                                    'md' => 4,
                                    'lg' => 1,
                                    'xl' => 1,
                                ]),
                            TextInput::make('phone_2')
                                ->label(__('users.forms.fields.phone_2'))
                                ->mask(RawJs::make(<<<'JS'
                                    $input.length > 10 ? '(99) 99999-9999' : '(99) 9999-9999'
                                JS))
                                ->columnSpan([
                                    'xs' => 4,
                                    'sm' => 4,
                                    'md' => 4,
                                    'lg' => 1,
                                    'xl' => 1,
                                ]),
                        ]),
                        Section::make('Permissionamento')
                            ->schema([
                                Grid::make(4)->schema([
                                    Select::make('role')
                                        ->label(__('users.forms.fields.role'))
                                        ->required()
                                        ->reactive()
                                        ->selectablePlaceholder(false)
                                        ->options(RoleEnum::getTranslated()),
                                ]),
                                Fieldset::make('Extras')
                                    ->visible(fn(Get $get): bool => $get('role') === RoleEnum::Salesman->value)
                                    ->schema([
                                        Grid::make(4)->schema([
                                            Toggle::make('update_payment_methods')->label(__('permissions.update_payment_methods')),
                                            Toggle::make('update_products')->label(__('permissions.update_products')),
                                            Toggle::make('update_price_tables')->label(__('permissions.update_price_tables')),
                                            Toggle::make('update_hauliers')->label(__('permissions.update_hauliers')),
                                        ]),
                                    ]),
                            ]),
                    ]),
                    Tab::make('Lançamento de pedidos')->schema([
                        Grid::make(2)->schema([
                            Toggle::make('inputs_orders_payment_information')
                                ->label(__('users.forms.fields.inputs_orders_payment_information'))
                                ->default(true),
                            Toggle::make('inputs_orders_freight_information')
                                ->label(__('users.forms.fields.inputs_orders_freight_information'))
                                ->default(true),
                        ]),
                    ]),
                    Tab::make('Carteira de clientes')
                        ->visible(function (User $user, string $context, Get $get): bool {
                            if ($context === 'create') {
                                return $get('role') === RoleEnum::Salesman->value && auth()->user()->hasRole(RoleEnum::Administrator->value);
                            }

                            return $user->hasRole(RoleEnum::Salesman->value) && auth()->user()->hasRole(RoleEnum::Administrator->value);
                        })
                        ->schema([
                            Grid::make(1)->schema([
                                TableRepeater::make('user_customers')
                                    ->label('Clientes')
                                    ->relationship('userCustomers')
                                    ->addActionLabel('Adicionar cliente na carteira')
                                    ->headers([
                                        Header::make(__('customers.forms.fields.name')),
                                        Header::make(__('customers.forms.fields.tax_id_number')),
                                    ])
                                    ->schema([
                                        Select::make('customer_id')
                                            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                                            ->columnSpan(2)
                                            ->required()
                                            ->searchable()
                                            ->reactive()
                                            ->getSearchResultsUsing(fn(string $search): Collection => GetCustomersByNameTradingNameOrTaxIdentificationNumber::run($search, true))
                                            ->getOptionLabelUsing(fn(UserCustomer $record): ?string => $record->customer->name)
                                            ->afterStateHydrated(fn(?string $state, Set $set) => self::handleCustomerDependantFields($state, $set))
                                            ->afterStateUpdated(fn(?string $state, Set $set) => self::handleCustomerDependantFields($state, $set)),
                                        TextInput::make('customer_tax_id_number')
                                            ->disabled(),
                                    ])
                            ])
                        ]),
                    Tab::make('Política de preços')
                        ->visible(function (User $user, string $context, Get $get): bool {
                            if ($context === 'create') {
                                return $get('role') === RoleEnum::Salesman->value && auth()->user()->hasRole(RoleEnum::Administrator->value);
                            }

                            return $user->hasRole(RoleEnum::Salesman->value) && auth()->user()->hasRole(RoleEnum::Administrator->value);
                        })
                        ->schema([
                            Fieldset::make('Descontos')->schema([
                                Grid::make(4)->schema([
                                    TextInput::make('min_discount_percentage')
                                        ->label(__('users.forms.fields.min_discount_percentage'))
                                        ->mask(function (): RawJs {
                                            return RawJs::make(<<<'JS'
                                                $money($input, ',') + '%'
                                            JS);
                                        })
                                        ->formatStateUsing(fn(User $user): string => $user->friendly_min_discount_percentage),
                                    TextInput::make('max_discount_percentage')
                                        ->label(__('users.forms.fields.max_discount_percentage'))
                                        ->mask(function (): RawJs {
                                            return RawJs::make(<<<'JS'
                                                $money($input, ',') + '%'
                                            JS);
                                        })
                                        ->formatStateUsing(fn(User $user): string => $user->friendly_max_discount_percentage),
                                    TextInput::make('min_discount_amount')
                                        ->label(__('users.forms.fields.min_discount_amount'))
                                        ->mask(function (): RawJs {
                                            return RawJs::make(<<<'JS'
                                                'R$ ' + $money($input, ',')
                                            JS);
                                        })
                                        ->formatStateUsing(fn(User $user): string => $user->friendly_min_discount_amount),
                                    TextInput::make('max_discount_amount')
                                        ->label(__('users.forms.fields.max_discount_amount'))
                                        ->mask(function (): RawJs {
                                            return RawJs::make(<<<'JS'
                                                'R$ ' + $money($input, ',')
                                            JS);
                                        })
                                        ->formatStateUsing(fn(User $user): string => $user->friendly_max_discount_amount),
                                ]),
                            ]),
                        ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query): Builder {
                return $query->orderBy('name');
            })
            ->columns([
                TextColumn::make('name')
                    ->label(__('users.forms.fields.name')),
                TextColumn::make('email')
                    ->label(__('users.forms.fields.email')),
                TextColumn::make('roles.name')
                    ->label(__('users.forms.fields.role'))
                    ->formatStateUsing(function (User $record) {
                        return RoleEnum::getTranslated()[$record->getRoleNames()[0]];
                    }),
                IconColumn::make('active')
                    ->label(__('users.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                Filter::make('name')
                    ->form([TextInput::make('name')->label(__('users.forms.fields.name'))])
                    ->query(fn(Builder $query, array $data): Builder => $query->where('name', 'like', "%{$data['name']}%")),
                Filter::make('email')
                    ->form([TextInput::make('email')->label(__('users.forms.fields.email'))])
                    ->query(fn(Builder $query, array $data): Builder => $query->where('email', 'like', "%{$data['email']}%")),
                SelectFilter::make('active')
                    ->label(__('users.forms.fields.active'))
                    ->options([
                        true => 'Sim',
                        false => 'Não',
                    ])
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->using(fn(User $user): bool => DeleteUser::run($user))
                        ->hidden(fn(User $user): bool => $user->id === auth()->id())
                        ->successNotification(success_notification(__('users.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->successNotification(success_notification(__('users.responses.delete_batch.success'))),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            SmtpConfigurationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
            'view' => Pages\ViewUser::route('/{record}'),
        ];
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected static function handleCustomerDependantFields(?string $customerId, Set $set): ?Customer
    {
        if (is_null($customerId)) {
            $set('customer_tax_id_number', '');
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customer_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }
}
