<?php

namespace App\Filament\Clusters\Product\Resources\VariantResource\Pages;

use App\Filament\Clusters\Product\Resources\VariantResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageVariants extends ManageRecords
{
    protected static string $resource = VariantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->successNotification(success_notification(__('variants.responses.create.success'))),
        ];
    }
}
