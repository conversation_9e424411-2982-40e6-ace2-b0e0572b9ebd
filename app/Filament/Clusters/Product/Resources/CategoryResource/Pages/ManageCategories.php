<?php

namespace App\Filament\Clusters\Product\Resources\CategoryResource\Pages;

use App\Actions\Category\Integrations\GetThirdPartyCategories;
use App\Enums\RoleEnum;
use App\Filament\Clusters\Product\Resources\CategoryResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageCategories extends ManageRecords
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-categories')
                ->visible(auth()->user()->hasRole(RoleEnum::Administrator->value))
                ->label('Integrar categorias')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyCategories::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.product.resources.categories.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar as categorias neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->successNotification(success_notification(__('categories.responses.create.success'))),
        ];
    }
}
