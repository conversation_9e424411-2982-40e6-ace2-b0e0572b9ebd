<?php

namespace App\Filament\Clusters\BasicData\Resources\PaymentMethodResource\Pages;

use App\Actions\PaymentMethod\GetThirdPartyPaymentMethods;
use App\Enums\RoleEnum;
use App\Filament\Clusters\BasicData\Resources\PaymentMethodResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManagePaymentMethods extends ManageRecords
{
    protected static string $resource = PaymentMethodResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-payment-methods')
                ->visible(auth()->user()->hasRole(RoleEnum::Administrator->value))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyPaymentMethods::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.basic-data.resources.payment-methods.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar as formas de pagamento de preço neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make()
                ->successNotification(success_notification(__('payment_methods.responses.create.success'))),
        ];
    }
}
