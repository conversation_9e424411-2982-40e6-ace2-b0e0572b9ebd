<?php

namespace App\Filament\Clusters\BasicData\Resources\HaulierResource\Pages;

use App\Filament\Clusters\BasicData\Resources\HaulierResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateHaulier extends CreateRecord
{
    protected static string $resource = HaulierResource::class;

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('hauliers.responses.create.success'))->send();
    }
}
