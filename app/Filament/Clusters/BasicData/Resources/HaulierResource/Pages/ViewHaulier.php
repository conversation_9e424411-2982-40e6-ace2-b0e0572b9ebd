<?php

namespace App\Filament\Clusters\BasicData\Resources\HaulierResource\Pages;

use App\Filament\Clusters\BasicData\Resources\HaulierResource;
use App\Models\Haulier;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewHaulier extends ViewRecord
{
    protected static string $resource = HaulierResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    /**
     * @inheritDoc
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Haulier $haulier */
        $haulier = Haulier::find($data['id']);

        $haulier->load(['city', 'state']);

        $data['address_city'] = $haulier?->city?->name;
        $data['address_state'] = $haulier?->state?->abbreviation;

        return $data;
    }
}
