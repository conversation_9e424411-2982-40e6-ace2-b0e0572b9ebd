<?php

namespace App\Filament\Clusters\BasicData\Resources\HaulierResource\Pages;

use App\Filament\Clusters\BasicData\Resources\HaulierResource;
use App\Models\Haulier;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditHaulier extends EditRecord
{
    protected static string $resource = HaulierResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->successRedirectUrl(route('filament.app.basic-data.resources.hauliers.index'))
                ->successNotification(success_notification(__('hauliers.responses.delete.success')))
                ->failureNotification(error_notification()),
        ];
    }

    /**
     * @inheritDoc
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Haulier $haulier */
        $haulier = Haulier::find($data['id']);

        $haulier->load(['city', 'state']);

        $data['address_city'] = $haulier?->city?->name;
        $data['address_state'] = $haulier?->state?->abbreviation;

        return $data;
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('hauliers.responses.update.success'))->send();
    }
}
