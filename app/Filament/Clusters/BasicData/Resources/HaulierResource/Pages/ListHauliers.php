<?php

namespace App\Filament\Clusters\BasicData\Resources\HaulierResource\Pages;

use App\Actions\Haulier\GetThirdPartyHauliers;
use App\Enums\RoleEnum;
use App\Filament\Clusters\BasicData\Resources\HaulierResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ListRecords;
use Throwable;

class ListHauliers extends ListRecords
{
    protected static string $resource = HaulierResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('get-third-party-hauliers')
                ->visible(auth()->user()->hasRole(RoleEnum::Administrator->value))
                ->label('Integrar')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Select::make('data_consumption_type')
                            ->label('Tipo de consumo')
                            ->options([
                                false => 'Última integração executada',
                                true => 'Forçar integração',
                            ])
                            ->selectablePlaceholder(false),
                    ]),
                ])
                ->action(function (array $data) {
                    try {
                        GetThirdPartyHauliers::run((bool) $data['data_consumption_type']);
                        success_notification('O processo está rodando em segundo plano.')->send();
                        return redirect()->route('filament.app.basic-data.resources.hauliers.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível integrar as transportadoras neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->requiresConfirmation(),
            CreateAction::make(),
        ];
    }
}
