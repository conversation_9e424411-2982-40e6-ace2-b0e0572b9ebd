<?php

namespace App\Filament\Clusters\BasicData\Resources;

use App\Actions\Haulier\DeleteHaulier;
use App\Filament\Clusters\BasicData;
use App\Filament\Clusters\BasicData\Resources\HaulierResource\Pages;
use App\Http\Integrations\Receita\Services\ReceitaCnpjService;
use App\Http\Integrations\ViaCep\Services\ViaCepZipcodeService;
use App\Models\City;
use App\Models\Haulier;
use App\Models\State;
use Closure;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Throwable;

class HaulierResource extends Resource
{
    protected static ?string $model = Haulier::class;
    protected static ?string $cluster = BasicData::class;
    protected static ?string $modelLabel = 'transportadora';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationIcon = 'heroicon-o-truck';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Identificação')
                    ->compact()
                    ->schema([
                        Grid::make(4)->schema([
                            TextInput::make('tax_id_number')
                                ->required()
                                ->label(__('hauliers.forms.fields.tax_id_number'))
                                ->columnSpan(1)
                                ->minLength(14)
                                ->maxLength(18)
                                ->formatStateUsing(fn(?Haulier $haulier): string => $haulier?->friendly_tax_id_number ?? '')
                                ->mask(RawJs::make(<<<'JS'
                                    $input.length > 14 ? '99.999.999/9999-99' : '999.999.999-99'
                                JS))
                                ->lazy()
                                ->afterStateUpdated(function (?string $state, Set $set) {
                                    if (strlen($state) !== 18) {
                                        return;
                                    }

                                    try {
                                        $companyDetails = ReceitaCnpjService::make()->getCompanyDetails($state);
                                    } catch (Throwable $th) {
                                        error($th);
                                        error_notification('Não foi possível obter os dados da receita.')->send();
                                        return;
                                    }

                                    if (isset($companyDetails->status) && $companyDetails->status !== 'OK') {
                                        return;
                                    }

                                    /** @var \App\Models\City $city */
                                    $city = City::query()
                                        ->where('name', $companyDetails->municipio)
                                        ->first();

                                    /** @var \App\Models\State $addressState */
                                    $addressState = State::query()
                                        ->where('abbreviation', $companyDetails->uf)
                                        ->first();

                                    $set('name', $companyDetails->nome);
                                    $set('trading_name', $companyDetails->fantasia);
                                    $set('email', $companyDetails->email);
                                    $set('billing_email', $companyDetails->email);
                                    $set('address_zipcode', mask_zipcode(get_numbers($companyDetails->cep)));
                                    $set('address_street', $companyDetails->logradouro);
                                    $set('address_number', $companyDetails->numero);
                                    $set('address_additional_info', $companyDetails->complemento);
                                    $set('address_district', $companyDetails->bairro);
                                    $set('address_city_id', $city->id);
                                    $set('address_city', $city->name);
                                    $set('address_state_id', $addressState->id);
                                    $set('address_state', $addressState->abbreviation);
                                })
                                ->rules([
                                    fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                        if (strlen($value) <= 14 && !validate_cpf($value)) {
                                            $fail('O CPF é inválido.');
                                        }
                                    }
                                ]),
                            TextInput::make('name')
                                ->required()
                                ->label(__('hauliers.forms.fields.name'))
                                ->columnSpan(2),
                            TextInput::make('trading_name')
                                ->label(__('hauliers.forms.fields.trading_name')),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('email')
                                ->label(__('hauliers.forms.fields.email'))
                                ->rule('email')
                                ->required()
                                ->columnSpan(2),
                            TextInput::make('phone_1')
                                ->label(__('hauliers.forms.fields.phone_1'))
                                ->mask(RawJs::make(<<<'JS'
                                    $input.length > 14 ? '(99) 99999-9999' : '(99) 9999-9999'
                                JS)),
                            TextInput::make('phone_2')
                                ->label(__('hauliers.forms.fields.phone_2'))
                                ->mask(RawJs::make(<<<'JS'
                                    $input.length > 14 ? '(99) 99999-9999' : '(99) 9999-9999'
                                JS)),
                        ]),
                    ]),
                Section::make('Endereço')
                    ->compact()
                    ->collapsed(fn(string $context): bool => $context === 'create')
                    ->schema([
                        Grid::make(4)->schema([
                            TextInput::make('address_zipcode')
                                ->label(__('hauliers.forms.fields.address_zipcode'))
                                ->mask('99999-999')
                                ->columnSpan(1)
                                ->lazy()
                                ->afterStateUpdated(function (?string $state, Set $set) {
                                    if (is_null($state) || trim($state) === '') {
                                        return;
                                    }

                                    try {
                                        $fullAddress = ViaCepZipcodeService::make()->getZipcodeDetails($state);
                                    } catch (Throwable $th) {
                                        error($th);
                                        error_notification('Não foi possível obter os dados automáticos do CEP.')->send();

                                        $set('address_street', null);
                                        $set('address_district', null);
                                        $set('address_city_id', null);
                                        $set('address_city', null);
                                        $set('address_state_id', null);
                                        $set('address_state', null);

                                        return;
                                    }

                                    if (isset($fullAddress->erro) && $fullAddress->erro) {
                                        $set('address_street', null);
                                        $set('address_district', null);
                                        $set('address_city_id', null);
                                        $set('address_city', null);
                                        $set('address_state_id', null);
                                        $set('address_state', null);
                                        return;
                                    }

                                    /** @var \App\Models\City $city */
                                    $city = City::find($fullAddress->ibge);

                                    /** @var \App\Models\State $addressState */
                                    $addressState = State::query()
                                        ->where('abbreviation', $fullAddress->uf)
                                        ->first();

                                    $set('address_street', $fullAddress->logradouro);
                                    $set('address_district', $fullAddress->bairro);
                                    $set('address_city_id', $city->id);
                                    $set('address_city', $city->name);
                                    $set('address_state_id', $addressState->id);
                                    $set('address_state', $addressState->name);
                                })
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('address_street')
                                ->label(__('hauliers.forms.fields.address_street'))
                                ->columnSpan(2),
                            TextInput::make('address_number')
                                ->label(__('hauliers.forms.fields.address_number'))
                                ->columnSpan(1),
                            TextInput::make('address_additional_info')
                                ->label(__('hauliers.forms.fields.address_additional_info'))
                                ->columnSpan(1),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('address_district')
                                ->label(__('hauliers.forms.fields.address_district'))
                                ->columnSpan(2),
                            Hidden::make('address_city_id'),
                            TextInput::make('address_city')
                                ->label(__('hauliers.forms.fields.address_city'))
                                ->disabled()
                                ->columnSpan(1),
                            Hidden::make('address_state_id'),
                            TextInput::make('address_state')
                                ->label(__('hauliers.forms.fields.address_state'))
                                ->disabled()
                                ->columnSpan(1),
                        ]),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('hauliers.forms.fields.name')),
                TextColumn::make('trading_name')
                    ->label(__('hauliers.forms.fields.trading_name')),
                TextColumn::make('tax_id_number')
                    ->label(__('hauliers.forms.fields.tax_id_number'))
                    ->formatStateUsing(fn(Haulier $haulier): string => $haulier->friendly_tax_id_number),
            ])
            ->filters([
                Filter::make('name')
                    ->form([TextInput::make('name')->label(__('hauliers.forms.fields.name'))])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(!is_null($data['name']) && $data['name'] !== '', function (Builder $query) use ($data): Builder {
                            return $query->where('name', 'like', "%{$data['name']}%");
                        });
                    }),
                Filter::make('trading_name')
                    ->form([TextInput::make('trading_name')->label(__('hauliers.forms.fields.trading_name'))])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(!is_null($data['trading_name']) && $data['trading_name'] !== '', function (Builder $query) use ($data): Builder {
                            return $query->where('trading_name', 'like', "%{$data['trading_name']}%");
                        });
                    }),
                Filter::make('tax_id_number')
                    ->form([TextInput::make('tax_id_number')->label(__('hauliers.forms.fields.tax_id_number'))])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(!is_null($data['tax_id_number']) && $data['tax_id_number'] !== '', function (Builder $query) use ($data): Builder {
                            return $query->where('tax_id_number', 'like', '%' . Str::remove(['-', '.', '/'], $data['tax_id_number']) . '%');
                        });
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->using(function (Haulier $haulier): bool {
                            try {
                                DeleteHaulier::run($haulier);
                                return true;
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                                return false;
                            }
                        })
                        ->successNotification(success_notification(__('hauliers.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function (Collection $records): bool {
                            try {
                                $records->each(fn(Haulier $haulier) => DeleteHaulier::run($haulier));
                                return true;
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                                return false;
                            }
                        })
                        ->successNotification(success_notification(__('hauliers.responses.delete_batch.success'))),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->emptyStateHeading('Ainda sem transportadoras')
            ->emptyStateDescription('Assim que você cadastrar suas transportadoras, elas aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHauliers::route('/'),
            'create' => Pages\CreateHaulier::route('/create'),
            'edit' => Pages\EditHaulier::route('/{record}/edit'),
            'view' => Pages\ViewHaulier::route('/{record}'),
        ];
    }
}
