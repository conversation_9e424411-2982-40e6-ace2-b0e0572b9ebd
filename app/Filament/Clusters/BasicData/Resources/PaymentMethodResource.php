<?php

namespace App\Filament\Clusters\BasicData\Resources;

use App\Actions\PaymentMethod\DeletePaymentMethod;
use App\Filament\Clusters\BasicData;
use App\Filament\Clusters\BasicData\Resources\PaymentMethodResource\Pages;
use App\Models\PaymentMethod;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Throwable;

class PaymentMethodResource extends Resource
{
    protected static ?string $model = PaymentMethod::class;
    protected static ?string $cluster = BasicData::class;
    protected static ?string $modelLabel = 'forma de pagamento';
    protected static ?string $pluralModelLabel = 'formas de pagamento';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(1)->schema([
                    TextInput::make('name')
                        ->label(__('payment_methods.forms.fields.name'))
                        ->required(),
                ]),
                Grid::make(1)->schema([
                    Toggle::make('active')
                        ->label(__('payment_methods.forms.fields.active'))
                        ->default(true),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn(Builder $query): Builder => $query->orderBy('name'))
            ->columns([
                TextColumn::make('name')
                    ->label(__('payment_methods.forms.fields.name')),
                IconColumn::make('active')
                    ->label(__('payment_methods.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                Filter::make('name')
                    ->form([TextInput::make('name')->label(__('payment_methods.forms.fields.name'))])
                    ->query(fn(Builder $query, array $data): Builder => $query->where('name', 'like', "%{$data['name']}%")),
                SelectFilter::make('active')
                    ->label(__('payment_methods.forms.fields.active'))
                    ->options([
                        true => 'Sim',
                        false => 'Não',
                    ])
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('payment_methods.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->using(function (PaymentMethod $paymentMethod): bool {
                            try {
                                DeletePaymentMethod::run($paymentMethod);
                                return true;
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                                return false;
                            }
                        })
                        ->successNotification(success_notification(__('payment_methods.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->using(function (Collection $records): bool {
                            try {
                                $records->each(fn(PaymentMethod $paymentMethod) => DeletePaymentMethod::run($paymentMethod));
                                return true;
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                                return false;
                            }
                        })
                        ->successNotification(success_notification(__('payment_methods.responses.delete_batch.success'))),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->successNotification(success_notification(__('payment_methods.responses.create.success'))),
            ])
            ->emptyStateHeading('Ainda sem formas de pagamento')
            ->emptyStateDescription('Assim que você cadastrar suas formas de pagamento, elas aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePaymentMethods::route('/'),
        ];
    }
}
