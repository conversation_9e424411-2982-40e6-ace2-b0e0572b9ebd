<?php

namespace App\Filament\Clusters\Users\Resources;

use App\Actions\Team\DeleteTeam;
use App\Core\Filament\Filters\P4SellTextFilter;
use App\Filament\Clusters\AccessControl;
use App\Filament\Clusters\Users\Resources\TeamResource\Pages;
use App\Models\CommissionRule;
use App\Models\Team;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Throwable;

class TeamResource extends Resource
{
    protected static ?string $model = Team::class;
    protected static ?string $cluster = AccessControl::class;
    protected static ?string $modelLabel = 'equipe';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('teams.forms.fields.name'))
                    ->required(),
            ]),
            Grid::make(2)->schema([
                Select::make('manager_id')
                    ->relationship('manager', 'name', function (Builder $query): Builder {
                        return $query->whereDoesntHave('teamUsers');
                    })
                    ->label(__('teams.forms.fields.manager_id'))
                    ->required(),
                Select::make('commission_rule_id')
                    ->label(__('teams.forms.fields.commission_rule_id'))
                    ->relationship('commissionRule', titleAttribute: 'name')
                    ->required()
                    ->options(function (): Collection {
                        return CommissionRule::query()
                            ->get()
                            ->pluck('name', 'id');
                    })
            ]),
            Fieldset::make('Comissionamento')->schema([
                Grid::make(1)->schema([
                    Toggle::make('consider_category_commission_rule_tiers')
                        ->label(__('teams.forms.fields.consider_category_commission_rule_tiers'))
                        ->default(false),
                ]),
            ]),
            Fieldset::make('Vendedores')->schema([
                Grid::make(1)->schema([
                    TableRepeater::make('team_users')
                        ->hiddenLabel()
                        ->relationship('teamUsers')
                        ->reorderable(false)
                        ->addActionLabel('Adicionar vendedor à equipe')
                        ->headers([
                            Header::make('Vendedor'),
                        ])
                        ->schema([
                            Select::make('user_id')
                                ->relationship('user', 'name')
                                ->required(),
                        ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('teams.forms.fields.name')),
                TextColumn::make('manager.name')
                    ->label(__('teams.forms.fields.manager_id')),
            ])
            ->filters([
                P4SellTextFilter::buildLike('teams', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->using(function (Team $team): void {
                            try {
                                DeleteTeam::run($team);
                                success_notification(__('teams.responses.delete.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        }),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->emptyStateHeading('Ainda sem equipes')
            ->emptyStateDescription('Assim que você cadastrar suas equipes, elas aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeams::route('/'),
            'create' => Pages\CreateTeam::route('/create'),
            'edit' => Pages\EditTeam::route('/{record}/edit'),
            'view' => Pages\ViewTeam::route('/{record}'),
        ];
    }
}
