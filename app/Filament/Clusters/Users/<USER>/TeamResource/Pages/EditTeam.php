<?php

namespace App\Filament\Clusters\Users\Resources\TeamResource\Pages;

use App\Actions\Team\DeleteTeam;
use App\Filament\Clusters\Users\Resources\TeamResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Throwable;

class EditTeam extends EditRecord
{
    protected static string $resource = TeamResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->using(function () {
                    try {
                        DeleteTeam::run($this->record);
                        success_notification(__('teams.responses.delete.success'))->send();
                        return redirect()->route('filament.app.users.resources.teams.index');
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }

    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('teams.responses.update.success'));
    }
}
