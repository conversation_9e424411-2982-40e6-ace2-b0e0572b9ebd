<?php

namespace App\Filament\Clusters\Users\Resources\TeamResource\Pages;

use App\Filament\Clusters\Users\Resources\TeamResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewTeam extends ViewRecord
{
    protected static string $resource = TeamResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('teams.responses.update.success'));
    }
}
