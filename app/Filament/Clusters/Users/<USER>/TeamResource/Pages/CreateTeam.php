<?php

namespace App\Filament\Clusters\Users\Resources\TeamResource\Pages;

use App\Filament\Clusters\Users\Resources\TeamResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateTeam extends CreateRecord
{
    protected static string $resource = TeamResource::class;

    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('teams.responses.create.success'));
    }
}
